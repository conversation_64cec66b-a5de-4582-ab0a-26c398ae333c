import React, { useState, useEffect, useCallback } from 'react'
import { useNavigate } from 'react-router-dom'
import { ComplaintData } from '../../../shared/api'
import TransactionTable from '../components/TransactionTable'
import EnhancedComplaintTable from '../components/EnhancedComplaintTable'
import { Button } from '../components/ui/Button.tsx'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card'
import UnifiedNavbar from '../components/UnifiedNavbar'
import {
  FullWidthLayout,
  ResponsiveGrid,
  FullWidthSection,
  useResponsiveBreakpoint
} from '../components/layout/FullWidthLayout'

interface Complaint {
  id: string
  complaint_number: string
  complainant_name: string
  category_of_fraud: string
  date_of_complaint: string
  amount: number
  status: string
  metadata?: Record<string, unknown>
}

const Dashboard: React.FC = () => {
  const navigate = useNavigate()
  const breakpoint = useResponsiveBreakpoint()
  const [complaints, setComplaints] = useState<Complaint[]>([])
  const [complaintData, setComplaintData] = useState<ComplaintData[]>([])
  const [loading, setLoading] = useState(true)
  const [useEnhancedTable, setUseEnhancedTable] = useState(true)

  const fetchComplaints = useCallback(async (): Promise<void> => {
    try {
      setLoading(true)
      const dbComplaints: ComplaintData[] = await window.api.database.getComplaints()

      // Store original data for enhanced table
      setComplaintData(dbComplaints)

      // Transform for legacy table
      const transformedComplaints = dbComplaints.map((c) => ({
        id: c.id,
        complaint_number: (c.metadata?.complaint_number as string) || '',
        complainant_name: (c.metadata?.complainant_name as string) || '',
        category_of_fraud: (c.metadata?.subcategory as string) || '',
        date_of_complaint: (c.metadata?.date as string) || c.created_at,
        amount: parseFloat(String(c.metadata?.total_amount || 0).replace(/,/g, '')),
        status: c.status || 'unknown',
        metadata: c.metadata || {}
      }))
      setComplaints(transformedComplaints as Complaint[])
    } catch (error) {
      console.error('Failed to fetch complaints:', error)
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchComplaints()
  }, [])

  const handleView = useCallback(
    (complaintId: string): void => {
      navigate(`/complaint-details/${complaintId}`)
    },
    [navigate]
  )

  const handleEdit = useCallback(
    async (editedComplaint: Complaint): Promise<void> => {
      try {
        const originalComplaint = complaints.find((c) => c.id === editedComplaint.id)
        if (!originalComplaint) {
          console.error('Original complaint not found for editing')
          return
        }

        const updatedMetadata = {
          ...originalComplaint.metadata,
          complaint_number: editedComplaint.complaint_number,
          complainant_name: editedComplaint.complainant_name,
          subcategory: editedComplaint.category_of_fraud
        }

        const dataToUpdate = {
          metadata: updatedMetadata,
          status: editedComplaint.status
        }

        await window.api.database.updateComplaint(editedComplaint.id, dataToUpdate)

        setComplaints((prev) =>
          prev.map((c) =>
            c.id === editedComplaint.id ? { ...editedComplaint, metadata: updatedMetadata } : c
          )
        )
      } catch (error) {
        console.error('Failed to save complaint:', error)
      }
    },
    [complaints]
  ) // Dependency on complaints to ensure latest state for find

  const handleDelete = useCallback(
    (complaintNumber: string): void => {
      // Implement delete logic
      console.log('Deleting:', complaintNumber)
      setComplaints(complaints.filter((c) => c.complaint_number !== complaintNumber))
    },
    [complaints]
  )

  const handleSummary = useCallback(
    (complaintNumber: string): void => {
      navigate(`/complaints/summary/${complaintNumber}`)
    },
    [navigate]
  )



  return (
    <FullWidthLayout
      enableLampBackground={false}
      enableGlassmorphism={true}
      maxWidth="none"
      padding="none"
      className="min-h-screen"
    >
      <UnifiedNavbar
        title="Dashboard"
        subtitle="Manage and view all complaints"
      />

      <main className="flex-1 p-6 w-full">

        {/* Complaints Table Section */}
        <FullWidthSection
          title="Complaints"
          subtitle={`${complaintData.length} total complaints`}
        >
          <EnhancedComplaintTable
            complaints={complaintData}
            loading={loading}
            onRefresh={fetchComplaints}
          />
        </FullWidthSection>
      </main>
    </FullWidthLayout>
  )
}

export default Dashboard
