import React, { useState, useCallback, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { BarChart3, FileText, Download } from 'lucide-react'
import { backendService } from '../services/backendService'
import { ComplaintData, TransactionData } from '../../../shared/api'
import {
  EnhancedModernTable,
  EnhancedColumnDef,
  CRUDOperations
} from '../components/ui/enhanced-modern-table'
import UnifiedNavbar from '../components/UnifiedNavbar'
import { FullWidthLayout, FullWidthSection } from '../components/layout/FullWidthLayout'
import { useThemeContext } from '../context/useThemeContext'
import { cn } from '../lib/aceternity-utils'

// Transaction table row interface
interface TransactionTableRow {
  id: string
  fraud_type: string
  sender_account: string
  sender_transaction_id: string
  sender_bank: string
  layer: number
  txn_type: string
  type: string
  date: string
  receiver_bank: string
  receiver_account: string
  receiver_transaction_id: string
  amount: string
  receiver_ifsc: string
  receiver_info: string
  reference: string
  sr_no: string
}

const ComplaintDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()

  const [complaint, setComplaint] = useState<ComplaintData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isDownloading, setIsDownloading] = useState(false)
  const { isDark } = useThemeContext() // Define loadComplaint outside useEffect to be callable
  const loadComplaint = useCallback(async (): Promise<void> => {
    if (!id) return

    try {
      setLoading(true)
      const complaintData = await backendService.getLocalComplaint(id)
      if (complaintData) {
        setComplaint(complaintData)
      } else {
        setError('Complaint not found')
      }
    } catch (err) {
      setError('Failed to load complaint data')
      console.error('Error loading complaint:', err)
    } finally {
      setLoading(false)
    }
  }, [id])

  // Load complaint data on component mount and id change
  useEffect(() => {
    loadComplaint()
  }, [loadComplaint])

  // Handle CSV download
  const handleDownloadCSV = useCallback(async () => {
    if (!complaint || !complaint.layer_transactions) return

    const allTransactions = Object.values(
      complaint.layer_transactions
    ).flat() as import('../../../shared/api').TransactionData[]

    if (allTransactions.length === 0) return

    setIsDownloading(true)
    try {
      // Generate CSV from transactions
      const headers = [
        'Layer',
        'Fraud Type',
        'Sender Account',
        'Sender Txn ID',
        'Sender Bank',
        'Receiver Account',
        'Txn ID',
        'Receiver Bank',
        'Txn Type',
        'Txn Date',
        'Amount',
        'Reference'
      ]

      let csvContent = headers.join(',') + '\n'

      allTransactions.forEach((txn: import('../../../shared/api').TransactionData) => {
        const row = [
          txn.layer || '',
          txn.fraud_type || 'banking_upi',
          txn.sender_account || '',
          txn.sender_transaction_id || '',
          txn.sender_bank || '',
          txn.receiver_account || '',
          txn.receiver_transaction_id || '',
          txn.receiver_bank || '',
          txn.type || txn.txn_type || '',
          txn.date || '',
          txn.amount || '',
          txn.reference || txn.receiver_info || ''
        ]

        const escapedRow = row.map((field) => {
          if (field && field.toString().includes(',')) {
            return `"${field}"`
          }
          return field
        })

        csvContent += escapedRow.join(',') + '\n'
      })

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `complaint_${id}_transactions.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } catch (error) {
      console.error('Failed to download CSV:', error)
    } finally {
      setIsDownloading(false)
    }
  }, [complaint, id])

  // Convert TransactionData to TransactionTableRow
  const convertToTableRows = useCallback(
    (transactions: TransactionData[]): TransactionTableRow[] => {
      return transactions.map((txn, index) => ({
        id: `txn-${index}`,
        fraud_type: txn.fraud_type || '',
        sender_account: txn.sender_account || '',
        sender_transaction_id: txn.sender_transaction_id || '',
        sender_bank: txn.sender_bank || '',
        layer: txn.layer || 0,
        txn_type: txn.txn_type || '',
        type: txn.type || '',
        date: txn.date || '',
        receiver_bank: txn.receiver_bank || '',
        receiver_account: txn.receiver_account || '',
        receiver_transaction_id: txn.receiver_transaction_id || '',
        amount: txn.amount || '',
        receiver_ifsc: txn.receiver_ifsc || '',
        receiver_info: txn.receiver_info || '',
        reference: txn.reference || '',
        sr_no: txn.sr_no || ''
      }))
    },
    []
  )

  // Convert TransactionTableRow back to TransactionData
  const convertToTransactionData = useCallback((row: TransactionTableRow): TransactionData => {
    return {
      fraud_type: row.fraud_type,
      sender_account: row.sender_account,
      sender_transaction_id: row.sender_transaction_id,
      sender_bank: row.sender_bank,
      layer: row.layer,
      txn_type: row.txn_type,
      type: row.type,
      date: row.date,
      receiver_bank: row.receiver_bank,
      receiver_account: row.receiver_account,
      receiver_transaction_id: row.receiver_transaction_id,
      amount: row.amount,
      receiver_ifsc: row.receiver_ifsc,
      receiver_info: row.receiver_info,
      reference: row.reference,
      sr_no: row.sr_no,
      extracted_at: new Date().toISOString(),
      source: 'manual_edit',
      is_valid: 'true',
      validation_errors: ''
    }
  }, [])

  // Define table columns
  const columns: EnhancedColumnDef<TransactionTableRow>[] = [
    {
      key: 'layer',
      title: 'Layer',
      type: 'number',
      editable: true,
      width: 80
    },
    {
      key: 'txn_type',
      title: 'Transaction Type',
      type: 'text',
      editable: true,
      width: 120
    },
    {
      key: 'sender_account',
      title: 'Sender Account',
      type: 'text',
      editable: true,
      width: 150
    },
    {
      key: 'sender_bank',
      title: 'Sender Bank',
      type: 'text',
      editable: true,
      width: 120
    },
    {
      key: 'receiver_account',
      title: 'Receiver Account',
      type: 'text',
      editable: true,
      width: 150
    },
    {
      key: 'receiver_bank',
      title: 'Receiver Bank',
      type: 'text',
      editable: true,
      width: 120
    },
    {
      key: 'amount',
      title: 'Amount',
      type: 'text',
      editable: true,
      width: 100
    },
    {
      key: 'date',
      title: 'Date',
      type: 'date',
      editable: true,
      width: 120
    },
    {
      key: 'reference',
      title: 'Reference',
      type: 'text',
      editable: true,
      width: 150
    }
  ]

  // CRUD operations
  const operations: CRUDOperations<TransactionTableRow> = {
    onUpdate: async (rowId: string, data: Partial<TransactionTableRow>) => {
      try {
        if (!id || !complaint) throw new Error('No complaint data')

        // Get all current transactions
        const allTransactions = Object.values(
          complaint.layer_transactions || {}
        ).flat() as TransactionData[]

        // Find and update the specific transaction
        const transactionIndex = parseInt(rowId.replace('txn-', ''))
        if (transactionIndex >= 0 && transactionIndex < allTransactions.length) {
          // Merge the updated data with existing transaction
          const existingTransaction = allTransactions[transactionIndex]
          const updatedTransaction = convertToTransactionData({
            ...convertToTableRows([existingTransaction])[0],
            ...data
          })
          allTransactions[transactionIndex] = updatedTransaction
        }

        // Reorganize by layers
        const updatedLayerTransactions: Record<string, TransactionData[]> = {}
        allTransactions.forEach((txn) => {
          const layer = txn.layer || 'default'
          if (!updatedLayerTransactions[layer]) {
            updatedLayerTransactions[layer] = []
          }
          updatedLayerTransactions[layer].push(txn)
        })

        // Update in database
        const success = await backendService.updateComplaint(id, {
          layer_transactions: updatedLayerTransactions
        })

        if (success) {
          await loadComplaint()
          // Return the updated row
          return convertToTableRows([allTransactions[transactionIndex]])[0]
        } else {
          throw new Error('Failed to update transaction')
        }
      } catch {
        throw new Error('Error updating transaction')
      }
    },

    onDelete: async (rowId: string) => {
      try {
        if (!id || !complaint) throw new Error('No complaint data')

        // Get all current transactions
        const allTransactions = Object.values(
          complaint.layer_transactions || {}
        ).flat() as TransactionData[]

        // Remove the specific transaction
        const transactionIndex = parseInt(rowId.replace('txn-', ''))
        if (transactionIndex >= 0 && transactionIndex < allTransactions.length) {
          allTransactions.splice(transactionIndex, 1)
        }

        // Reorganize by layers
        const updatedLayerTransactions: Record<string, TransactionData[]> = {}
        allTransactions.forEach((txn) => {
          const layer = txn.layer || 'default'
          if (!updatedLayerTransactions[layer]) {
            updatedLayerTransactions[layer] = []
          }
          updatedLayerTransactions[layer].push(txn)
        })

        // Update in database
        const success = await backendService.updateComplaint(id, {
          layer_transactions: updatedLayerTransactions
        })

        if (success) {
          await loadComplaint()
        } else {
          throw new Error('Failed to delete transaction')
        }
      } catch {
        throw new Error('Error deleting transaction')
      }
    }
  }

  // Navigate to graph visualization
  const handleGraphVisualization = (): void => {
    navigate(`/graph/${id}`)
  }

  // Navigate to notice generation
  const handleNoticeGeneration = (): void => {
    navigate(`/notices/${id}`)
  }

  // Navigate to stats page
  const handleStatsAnalysis = (): void => {
    navigate(`/stats/${id}`)
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full w-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6 w-full">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 rounded-lg p-4 text-red-800 dark:text-red-300">
          <p>{error}</p>
        </div>
      </div>
    )
  }

  if (!complaint) {
    return (
      <div className="p-6 w-full">
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800/50 rounded-lg p-4 text-yellow-800 dark:text-yellow-300">
          <p>No complaint data available</p>
        </div>
      </div>
    )
  }

  return (
    <FullWidthLayout
      enableLampBackground={false}
      enableGlassmorphism={true}
      maxWidth="none"
      padding="none"
      className="min-h-screen"
    >
      <UnifiedNavbar
        title={`Complaint Details - ${complaint.metadata?.complaint_number || complaint.title}`}
        showBackButton={true}
        customActions={
          <div className="flex items-center space-x-2">
            <button
              onClick={handleGraphVisualization}
              className="flex items-center gap-2 px-3 py-1.5 rounded-lg bg-blue-600 hover:bg-blue-700 text-white text-sm transition-colors"
            >
              <BarChart3 className="h-4 w-4" />
              Graph
            </button>
            <button
              onClick={handleNoticeGeneration}
              className="flex items-center gap-2 px-3 py-1.5 rounded-lg bg-green-600 hover:bg-green-700 text-white text-sm transition-colors"
            >
              <FileText className="h-4 w-4" />
              Notices
            </button>
            <button
              onClick={handleStatsAnalysis}
              className="flex items-center gap-2 px-3 py-1.5 rounded-lg bg-purple-600 hover:bg-purple-700 text-white text-sm transition-colors"
            >
              <BarChart3 className="h-4 w-4" />
              Stats
            </button>
            <button
              onClick={handleDownloadCSV}
              disabled={isDownloading || !complaint.transactions?.length}
              className="flex items-center gap-2 px-3 py-1.5 rounded-lg bg-gray-600 hover:bg-gray-700 text-white text-sm transition-colors disabled:opacity-50"
            >
              <Download className="h-4 w-4" />
              {isDownloading ? 'Downloading...' : 'CSV'}
            </button>
          </div>
        }
      />

      <main className="p-6">
        <FullWidthSection
          title="Transaction Data"
          subtitle={`${Object.values(complaint.layer_transactions || {}).flat().length} transactions found`}
          className="mb-8"
        >
          <div className="p-6">
            {complaint.layer_transactions &&
            Object.values(complaint.layer_transactions).flat().length > 0 ? (
              <EnhancedModernTable
                data={convertToTableRows(
                  Object.values(complaint.layer_transactions)
                    .flat()
                    .filter(
                      (item): item is import('../../../shared/api').TransactionData =>
                        typeof item === 'object' &&
                        item !== null &&
                        'txn_type' in item &&
                        'amount' in item
                    )
                )}
                columns={columns}
                operations={operations}
                loading={loading}
                config={{
                  enableSearch: true,
                  enablePagination: true,
                  enableSorting: true,
                  enableSelection: false,
                  enableGlassmorphism: true,
                  enableRowActions: true,
                  pageSize: 20,
                  searchPlaceholder: 'Search transactions...'
                }}
                emptyMessage="No transaction data available."
                className="w-full"
              />
            ) : (
              <div
                className={cn(
                  'p-8 rounded-lg backdrop-blur-md border shadow-lg text-center',
                  isDark
                    ? 'bg-white/10 border-white/10 text-white'
                    : 'bg-white/80 border-gray-200/50 text-gray-900'
                )}
              >
                <p className={cn('text-lg', isDark ? 'text-gray-400' : 'text-gray-600')}>
                  No transaction data available
                </p>
              </div>
            )}
          </div>
        </FullWidthSection>
      </main>
    </FullWidthLayout>
  )
}

export default ComplaintDetails
