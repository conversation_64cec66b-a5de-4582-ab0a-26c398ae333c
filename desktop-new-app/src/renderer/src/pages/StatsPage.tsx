import React, { useState, useEffect, useCallback } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { useComplaintData } from '../hooks/useComplaintData'
import Stats from '../components/Stats'
import { TransactionData, ComplaintData } from '../../../shared/api'
import UnifiedNavbar from '../components/UnifiedNavbar'
import {
  FullWidthLayout,
  FullWidthSection,
  ResponsiveGrid
} from '../components/layout/FullWidthLayout'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card'
import { Button } from '../components/ui/Button'
import { useThemeContext } from '../context/useThemeContext'
import { cn } from '../lib/aceternity-utils'
// Simple tabs implementation for this component

interface CommonAccount {
  account: string
  count: number
  complaints: string[]
  ifsc?: string
  bank?: string
  location?: {
    city?: string
    state?: string
    district?: string
    address?: string
  }
}

interface AccountHolder {
  account: string
  name?: string
  address?: string
  city?: string
  state?: string
  district?: string
  pincode?: string
}

const StatsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { isDark } = useThemeContext()
  const { data: complaint, loading, error } = useComplaintData(id || '')

  // State for tabs
  const [activeTab, setActiveTab] = useState<'individual' | 'common'>('individual')

  // State for common account analysis
  const [allComplaints, setAllComplaints] = useState<ComplaintData[]>([])
  const [selectedComplaints, setSelectedComplaints] = useState<string[]>([])
  const [commonAccounts, setCommonAccounts] = useState<CommonAccount[]>([])
  const [accountHolders, setAccountHolders] = useState<Map<string, AccountHolder>>(new Map())
  const [analysisLoading, setAnalysisLoading] = useState(false)

  // Extract all transactions from layer_transactions
  const allTransactions: TransactionData[] = React.useMemo(() => {
    if (!complaint?.layer_transactions) return []

    return Object.values(complaint.layer_transactions)
      .flat()
      .filter((item): item is TransactionData =>
        typeof item === 'object' &&
        item !== null &&
        'receiver_ifsc' in item
      )
  }, [complaint])

  // Fetch all complaints for common account analysis
  const fetchAllComplaints = useCallback(async (): Promise<void> => {
    try {
      const dbComplaints: ComplaintData[] = await window.api.database.getComplaints()
      setAllComplaints(dbComplaints)
      // Auto-select current complaint if available
      if (id && dbComplaints.some(c => c.id === id)) {
        setSelectedComplaints([id])
      }
    } catch (error) {
      console.error('Error fetching complaints:', error)
    }
  }, [id])

  useEffect(() => {
    fetchAllComplaints()
  }, [fetchAllComplaints])

  // Analyze common accounts across selected complaints
  const handleAnalyzeCommonAccounts = useCallback(async (): Promise<void> => {
    setAnalysisLoading(true)
    try {
      const selectedComplaintData = allComplaints.filter(c => selectedComplaints.includes(c.id))
      const accountMap: Record<string, { count: number; complaints: string[]; ifsc?: string; transactions: TransactionData[] }> = {}

      selectedComplaintData.forEach((complaint) => {
        if (complaint.layer_transactions) {
          Object.values(complaint.layer_transactions).flat().forEach((txn: any) => {
            if (txn && typeof txn === 'object') {
              const account = txn.receiver_account || txn.sender_account
              const ifsc = txn.receiver_ifsc || txn.sender_ifsc

              if (account) {
                if (!accountMap[account]) {
                  accountMap[account] = { count: 0, complaints: [], ifsc, transactions: [] }
                }
                accountMap[account].count++
                accountMap[account].transactions.push(txn)
                if (!accountMap[account].complaints.includes(complaint.title)) {
                  accountMap[account].complaints.push(complaint.title)
                }
                if (ifsc && !accountMap[account].ifsc) {
                  accountMap[account].ifsc = ifsc
                }
              }
            }
          })
        }
      })

      // Filter accounts that appear in multiple complaints or multiple times
      const commons = Object.entries(accountMap)
        .filter(([, v]) => v.count > 1 || v.complaints.length > 1)
        .map(([account, v]) => ({
          account,
          count: v.count,
          complaints: v.complaints,
          ifsc: v.ifsc
        }))

      // Fetch IFSC details for location information
      const enrichedCommons = await Promise.all(
        commons.map(async (common) => {
          if (common.ifsc) {
            try {
              const ifscResult = await window.api.ifsc.fetchIFSCDetails(common.ifsc)
              if (ifscResult.success && ifscResult.data) {
                return {
                  ...common,
                  bank: ifscResult.data.BANK,
                  location: {
                    city: ifscResult.data.CITY,
                    state: ifscResult.data.STATE,
                    district: ifscResult.data.DISTRICT,
                    address: ifscResult.data.ADDRESS
                  }
                }
              }
            } catch (error) {
              console.error(`Error fetching IFSC details for ${common.ifsc}:`, error)
            }
          }
          return common
        })
      )

      setCommonAccounts(enrichedCommons)
    } catch (error) {
      console.error('Error analyzing common accounts:', error)
    } finally {
      setAnalysisLoading(false)
    }
  }, [allComplaints, selectedComplaints])

  // Update account holder information
  const updateAccountHolder = useCallback((account: string, holderInfo: Partial<AccountHolder>): void => {
    setAccountHolders(prev => {
      const updated = new Map(prev)
      const existing = updated.get(account) || { account }
      updated.set(account, { ...existing, ...holderInfo })
      return updated
    })
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="text-lg">Loading complaint data...</div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-lg mb-2">Error</div>
          <div className="text-gray-600">{error}</div>
        </div>
      </div>
    )
  }

  if (!complaint) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="text-gray-600">Complaint not found</div>
        </div>
      </div>
    )
  }

  return (
    <FullWidthLayout
      enableLampBackground={false}
      enableGlassmorphism={true}
      maxWidth="none"
      padding="none"
      className="min-h-screen"
    >
      <UnifiedNavbar
        title={complaint ? `Statistics - ${complaint.title}` : "Statistics"}
        showBackButton
        customActions={
          <Button
            onClick={() => navigate('/all-stats')}
            variant="outline"
            size="sm"
          >
            All Stats
          </Button>
        }
      />

      <main className="flex-1 p-6 w-full">
        {/* Simple Tabs Implementation */}
        <div className="w-full">
          <div className="flex space-x-1 rounded-lg bg-gray-100 dark:bg-gray-800 p-1 mb-6">
            <button
              onClick={() => setActiveTab('individual')}
              className={cn(
                'flex-1 rounded-md px-3 py-2 text-sm font-medium transition-all',
                activeTab === 'individual'
                  ? 'bg-white text-gray-900 shadow-sm dark:bg-gray-700 dark:text-white'
                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
              )}
            >
              Individual Analysis
            </button>
            <button
              onClick={() => setActiveTab('common')}
              className={cn(
                'flex-1 rounded-md px-3 py-2 text-sm font-medium transition-all',
                activeTab === 'common'
                  ? 'bg-white text-gray-900 shadow-sm dark:bg-gray-700 dark:text-white'
                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
              )}
            >
              Common Account Analysis
            </button>
          </div>

          {activeTab === 'individual' && (
            <div className="space-y-6">
              <FullWidthSection
                title={`Statistics for ${complaint.title}`}
                subtitle="Detailed analysis of transactions and patterns"
                className="mb-8"
              >
                <Stats
                  transactions={allTransactions}
                  loading={loading}
                />
              </FullWidthSection>
            </div>
          )}

          {activeTab === 'common' && (
            <div className="space-y-6">
            <FullWidthSection
              title="Common Account Analysis"
              subtitle="Find accounts that appear across multiple complaints"
              className="mb-8"
            >
              <div className="space-y-6">
                {/* Complaint Selection */}
                <Card className={cn(
                  'backdrop-blur-md border shadow-lg',
                  isDark ? 'bg-white/10 border-white/10' : 'bg-white/80 border-gray-200/50'
                )}>
                  <CardHeader>
                    <CardTitle>Select Complaints to Analyze</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                      {allComplaints.map((complaint) => (
                        <label key={complaint.id} className="flex items-center space-x-2 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={selectedComplaints.includes(complaint.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedComplaints(prev => [...prev, complaint.id])
                              } else {
                                setSelectedComplaints(prev => prev.filter(id => id !== complaint.id))
                              }
                            }}
                            className="rounded"
                          />
                          <span className={cn(
                            'text-sm',
                            isDark ? 'text-white' : 'text-gray-900'
                          )}>
                            {complaint.title}
                          </span>
                        </label>
                      ))}
                    </div>
                    <div className="flex gap-4">
                      <Button
                        onClick={handleAnalyzeCommonAccounts}
                        disabled={selectedComplaints.length < 2 || analysisLoading}
                        className="min-w-[120px]"
                      >
                        {analysisLoading ? 'Analyzing...' : 'Analyze Common Accounts'}
                      </Button>
                      <Button
                        onClick={() => setSelectedComplaints(allComplaints.map(c => c.id))}
                        variant="outline"
                      >
                        Select All
                      </Button>
                      <Button
                        onClick={() => setSelectedComplaints([])}
                        variant="outline"
                      >
                        Clear All
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Common Accounts Results */}
                {commonAccounts.length > 0 && (
                  <Card className={cn(
                    'backdrop-blur-md border shadow-lg',
                    isDark ? 'bg-white/10 border-white/10' : 'bg-white/80 border-gray-200/50'
                  )}>
                    <CardHeader>
                      <CardTitle>Common Accounts Found ({commonAccounts.length})</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {commonAccounts.map((account, index) => (
                          <div key={account.account} className={cn(
                            'p-4 rounded-lg border',
                            isDark ? 'bg-white/5 border-white/10' : 'bg-gray-50 border-gray-200'
                          )}>
                            <div className="flex justify-between items-start mb-2">
                              <div>
                                <h4 className={cn(
                                  'font-semibold',
                                  isDark ? 'text-white' : 'text-gray-900'
                                )}>
                                  Account: {account.account}
                                </h4>
                                {account.ifsc && (
                                  <p className={cn(
                                    'text-sm',
                                    isDark ? 'text-gray-400' : 'text-gray-600'
                                  )}>
                                    IFSC: {account.ifsc}
                                  </p>
                                )}
                                {account.bank && (
                                  <p className={cn(
                                    'text-sm',
                                    isDark ? 'text-gray-400' : 'text-gray-600'
                                  )}>
                                    Bank: {account.bank}
                                  </p>
                                )}
                              </div>
                              <div className="text-right">
                                <span className="bg-blue-500 text-white px-2 py-1 rounded text-sm">
                                  {account.count} transactions
                                </span>
                              </div>
                            </div>

                            {account.location && (
                              <div className="mb-2">
                                <p className={cn(
                                  'text-sm font-medium',
                                  isDark ? 'text-gray-300' : 'text-gray-700'
                                )}>
                                  Location:
                                </p>
                                <p className={cn(
                                  'text-sm',
                                  isDark ? 'text-gray-400' : 'text-gray-600'
                                )}>
                                  {account.location.city}, {account.location.district}, {account.location.state}
                                </p>
                                {account.location.address && (
                                  <p className={cn(
                                    'text-xs',
                                    isDark ? 'text-gray-500' : 'text-gray-500'
                                  )}>
                                    {account.location.address}
                                  </p>
                                )}
                              </div>
                            )}

                            <div>
                              <p className={cn(
                                'text-sm font-medium mb-1',
                                isDark ? 'text-gray-300' : 'text-gray-700'
                              )}>
                                Appears in complaints:
                              </p>
                              <div className="flex flex-wrap gap-1">
                                {account.complaints.map((complaintTitle, i) => (
                                  <span key={i} className={cn(
                                    'px-2 py-1 rounded text-xs',
                                    isDark ? 'bg-white/10 text-white' : 'bg-gray-200 text-gray-800'
                                  )}>
                                    {complaintTitle}
                                  </span>
                                ))}
                              </div>
                            </div>

                            {/* Account Holder Information Input */}
                            <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                              <p className={cn(
                                'text-sm font-medium mb-2',
                                isDark ? 'text-gray-300' : 'text-gray-700'
                              )}>
                                Account Holder Information:
                              </p>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                <input
                                  type="text"
                                  placeholder="Holder Name"
                                  value={accountHolders.get(account.account)?.name || ''}
                                  onChange={(e) => updateAccountHolder(account.account, { name: e.target.value })}
                                  className={cn(
                                    'px-3 py-2 text-sm rounded border',
                                    isDark
                                      ? 'bg-white/10 border-white/20 text-white placeholder-gray-400'
                                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                                  )}
                                />
                                <input
                                  type="text"
                                  placeholder="Address"
                                  value={accountHolders.get(account.account)?.address || ''}
                                  onChange={(e) => updateAccountHolder(account.account, { address: e.target.value })}
                                  className={cn(
                                    'px-3 py-2 text-sm rounded border',
                                    isDark
                                      ? 'bg-white/10 border-white/20 text-white placeholder-gray-400'
                                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                                  )}
                                />
                                <input
                                  type="text"
                                  placeholder="City"
                                  value={accountHolders.get(account.account)?.city || ''}
                                  onChange={(e) => updateAccountHolder(account.account, { city: e.target.value })}
                                  className={cn(
                                    'px-3 py-2 text-sm rounded border',
                                    isDark
                                      ? 'bg-white/10 border-white/20 text-white placeholder-gray-400'
                                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                                  )}
                                />
                                <input
                                  type="text"
                                  placeholder="State"
                                  value={accountHolders.get(account.account)?.state || ''}
                                  onChange={(e) => updateAccountHolder(account.account, { state: e.target.value })}
                                  className={cn(
                                    'px-3 py-2 text-sm rounded border',
                                    isDark
                                      ? 'bg-white/10 border-white/20 text-white placeholder-gray-400'
                                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                                  )}
                                />
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </FullWidthSection>
            </div>
          )}
        </div>
      </main>
    </FullWidthLayout>
  )
}

export default StatsPage
