console.log('[Preload] Script loaded')
import { contextBridge, ipc<PERSON>ender<PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'
import { API } from '../shared/api'

declare global {
  interface Window {
    electron: typeof electronAPI
    api: API
  }
}
import {
  AUTH_LOGIN,
  AUTH_SIGNUP,
  AUTH_LOGOUT,
  AUTH_GET_SESSION,
  AUTH_HAS_TOKEN,
  AUTH_VERIFY_EMAIL,
  AUTH_RESEND_OTP,
  DB_STORE_COMPLAINT,
  DB_GET_COMPLAINTS,
  DB_GET_COMPLAINT,
  DB_DELETE_COMPLAINT,
  DB_UPDATE_COMPLAINT,
  DB_BACKUP_DATABASE,
  DB_DELETE_COMPLAINTS_BY_MONTH,
  API_UPLOAD_HTML,
  IFSC_VALIDATE,
  IFSC_FETCH_DETAILS
} from '../shared/ipc-channels'

// Custom APIs for renderer
const api: API = {
  auth: {
    login: (credentials) => ipcRenderer.invoke(AUTH_LOGIN, credentials),
    signup: (credentials) => ipcRenderer.invoke(AUTH_SIGNUP, credentials),
    logout: () => ipcRenderer.invoke(AUTH_LOGOUT),
    getSession: () => ipcRenderer.invoke(AUTH_GET_SESSION),
    hasToken: () => ipcRenderer.invoke(AUTH_HAS_TOKEN),
    setToken: (token: string) => ipcRenderer.invoke('auth:setToken', token),
    getToken: () => ipcRenderer.invoke('auth:getToken'),
    clearToken: () => ipcRenderer.invoke('auth:clearToken'),
    verifyEmail: (data) => {
      console.log('[Preload] api.auth.verifyEmail called with:', data)
      return ipcRenderer.invoke(AUTH_VERIFY_EMAIL, data)
    },
    resendOtp: (data) => ipcRenderer.invoke(AUTH_RESEND_OTP, data)
  },
  database: {
    storeComplaint: (complaint) => ipcRenderer.invoke(DB_STORE_COMPLAINT, complaint),
    getComplaints: () => ipcRenderer.invoke(DB_GET_COMPLAINTS),
    getComplaint: (id) => ipcRenderer.invoke(DB_GET_COMPLAINT, id),
    deleteComplaint: (id) => ipcRenderer.invoke(DB_DELETE_COMPLAINT, id),
    updateComplaint: (id, data) => ipcRenderer.invoke(DB_UPDATE_COMPLAINT, id, data)
  },
  uploadHtml: (data) => ipcRenderer.invoke(API_UPLOAD_HTML, data),
  backupDatabase: () => ipcRenderer.invoke(DB_BACKUP_DATABASE),
  deleteComplaintsByMonth: (month) => ipcRenderer.invoke(DB_DELETE_COMPLAINTS_BY_MONTH, month),
  ifsc: {
    validate: (ifscCode) => ipcRenderer.invoke(IFSC_VALIDATE, ifscCode),
    fetchDetails: (ifscCode) => ipcRenderer.invoke(IFSC_FETCH_DETAILS, ifscCode)
  }
}

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
    console.log('[Preload] api exposed successfully')
    console.log(
      '[Preload] typeof window.api?.auth?.verifyEmail:',
      typeof window.api?.auth?.verifyEmail
    )
    console.log('[Preload] window.api?.auth?.verifyEmail:', window.api?.auth?.verifyEmail)
  } catch (error) {
    console.error('[Preload] Error exposing api:', error)
  }
} else {
  window.electron = electronAPI
  window.api = api
  console.log('[Preload] api exposed to window (no context isolation)')
  console.log(
    '[Preload] typeof window.api?.auth?.verifyEmail:',
    typeof window.api?.auth?.verifyEmail
  )
  console.log('[Preload] window.api?.auth?.verifyEmail:', window.api?.auth?.verifyEmail)
}
